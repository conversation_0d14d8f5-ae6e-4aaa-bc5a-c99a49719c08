package httpserv

import (
	"digital-transformation-api/infrastructure"
	example "digital-transformation-api/internal/example-domain/handler"
	line "digital-transformation-api/internal/line/handler"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/gins/middleware"
	"fmt"
)

func Run() {
	app := gins.New()
	app.UseMiddleware(middleware.CORS())
	app.UseMiddleware(middleware.RouteContext()) // Add route context middleware
	app.UseMiddleware(middleware.Log())
	app.UseMiddleware(middleware.Error())

	example.BindPaymentRoute(app)
	line.BindStaffRoute(app)

	app.ListenAndServe(fmt.Sprintf(":%s", infrastructure.AppConfig.Port), closeFunc)
}

func closeFunc() {

}
