package domain

import "time"

// TncPdpa represents PDPA terms and conditions entries.
// Primary key: ID
type TncPdpa struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// UserType indicates the type of user the terms apply to
	UserType *string `json:"user_type,omitempty" db:"user_type"`
	// ConsentType indicates the type of consent
	ConsentType *string `json:"consent_type,omitempty" db:"consent_type"`
	// TermsVersion is the version of the terms
	TermsVersion *string `json:"terms_version,omitempty" db:"terms_version"`
	// Title is the title of the terms
	Title *string `json:"title,omitempty" db:"title"`
	// Content is the content/body of the terms
	Content *string `json:"content,omitempty" db:"content"`
	// Status indicates the current status of the terms
	Status *string `json:"status,omitempty" db:"status"`
	// ReleaseDate is when the terms were released
	ReleaseDate *time.Time `json:"release_date,omitempty" db:"release_date"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 