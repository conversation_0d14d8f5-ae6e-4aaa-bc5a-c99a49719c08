package domain

import "time"

// LeadLog represents a log entry related to a lead's status and remarks.
// Primary key: ID
// Foreign keys: CustomerID -> customer.id, StaffID -> staff.id, PromotionID -> promotion.id
type LeadLog struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// StaffID references the staff
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// PromotionID references the promotion
	PromotionID *int64 `json:"promotion_id,omitempty" db:"promotion_id"`
	// CustomerPhone is the logged customer phone
	CustomerPhone *string `json:"customer_phone,omitempty" db:"customer_phone"`
	// Remark is a free-form remark
	Remark *string `json:"remark,omitempty" db:"remark"`
	// Reason indicates a reason code
	Reason *string `json:"reason,omitempty" db:"reason"`
	// OtherReason captures any other reason
	OtherReason *string `json:"other_reason,omitempty" db:"other_reason"`
	// IsPotential indicates if the lead is considered potential
	IsPotential *bool `json:"is_potential,omitempty" db:"is_potential"`
	// Status indicates the current status of the log/lead
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 