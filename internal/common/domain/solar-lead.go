package domain

import "time"

// SolarLead represents a lead for solar products/services.
// Primary key: ID
// Foreign keys: GroupID -> customer_group.id, CustomerID -> customer.id, StaffID -> staff.id
type SolarLead struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the customer group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// StaffID references the staff owner
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// Firstname is the lead's first name
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the lead's last name
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the lead's email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the lead's phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// LineID is the LINE identifier
	LineID *string `json:"lineId,omitempty" db:"lineId"`
	// MonthlyElectricBill is the monthly electricity bill
	MonthlyElectricBill *float64 `json:"monthly_electric_bill,omitempty" db:"monthly_electric_bill"`
	// ResidenceType is the type of residence
	ResidenceType *string `json:"residence_type,omitempty" db:"residence_type"`
	// Meter is the meter type
	Meter *string `json:"meter,omitempty" db:"meter"`
	// Roof indicates roof information
	Roof *string `json:"roof,omitempty" db:"roof"`
	// RoofType is the roof type
	RoofType *string `json:"roof_type,omitempty" db:"roof_type"`
	// RoofRemark is the roof remark
	RoofRemark *string `json:"roof_remark,omitempty" db:"roof_remark"`
	// ConcurrentUsers is the number of concurrent users
	ConcurrentUsers *int32 `json:"concurrent_users,omitempty" db:"concurrent_users"`
	// ConcurrentAir indicates air conditioner concurrency
	ConcurrentAir *string `json:"concurrent_air,omitempty" db:"concurrent_air"`
	// ElectricityUsage indicates usage pattern
	ElectricityUsage *string `json:"electricity_usage,omitempty" db:"electricity_usage"`
	// Notes contains additional information
	Notes *string `json:"notes,omitempty" db:"notes"`
	// PreferredContactTime indicates the preferred time to contact
	PreferredContactTime *string `json:"preferred_contact_time,omitempty" db:"preferred_contact_time"`
	// Status indicates the current status of the lead
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 