package domain

import "time"

// PointSetting represents configuration for loyalty points.
// Primary key: ID
type PointSetting struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// Price is the base price for point calculation
	Price *float64 `json:"price,omitempty" db:"price"`
	// ExpiredAfterReceive is the number of days before points expire
	ExpiredAfterReceive *int32 `json:"expired_after_receive,omitempty" db:"expired_after_receive"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 