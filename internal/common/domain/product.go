package domain

import "time"

// Product represents a product record.
// Primary key: ID
// Foreign keys: GroupID -> product_group.id
type Product struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the product group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// Name is the product name
	Name *string `json:"name,omitempty" db:"name"`
	// Description is the product description
	Description *string `json:"description,omitempty" db:"description"`
	// SalePrice is the discounted price
	SalePrice *float64 `json:"sale_price,omitempty" db:"sale_price"`
	// RegularPrice is the original price
	RegularPrice *float64 `json:"regular_price,omitempty" db:"regular_price"`
	// Brand is the product brand
	Brand *string `json:"brand,omitempty" db:"brand"`
	// TypeOfDevice is the device type
	TypeOfDevice *string `json:"type_of_device,omitempty" db:"type_of_device"`
	// Status indicates the current status of the product
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 