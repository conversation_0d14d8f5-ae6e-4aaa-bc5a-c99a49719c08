package domain

import "time"

// TechRegis represents a technology registration lead.
// Primary key: ID
// Foreign keys: GroupID -> customer_group.id, CustomerID -> customer.id, StaffID -> staff.id, ProductIDs -> product.id, PromotionIDs -> promotion.id
type TechRegis struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the customer group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// StaffID references the staff owner
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// Firstname is the registration first name
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the registration last name
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the registration email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the registration phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// LineID is the LINE identifier
	LineID *string `json:"lineId,omitempty" db:"lineId"`
	// ProductIDs references a product identifier
	ProductIDs *int64 `json:"product_ids,omitempty" db:"product_ids"`
	// PromotionIDs references a promotion identifier
	PromotionIDs *int64 `json:"promotion_ids,omitempty" db:"promotion_ids"`
	// Provider is the current service provider
	Provider *string `json:"provider,omitempty" db:"provider"`
	// Price is the relevant price
	Price *float64 `json:"price,omitempty" db:"price"`
	// ResidenceType is the type of residence
	ResidenceType *string `json:"residence_type,omitempty" db:"residence_type"`
	// Notes contains additional information
	Notes *string `json:"notes,omitempty" db:"notes"`
	// PreferredContactTime indicates the preferred time to contact
	PreferredContactTime *string `json:"preferred_contact_time,omitempty" db:"preferred_contact_time"`
	// Status indicates the current status of the registration
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 