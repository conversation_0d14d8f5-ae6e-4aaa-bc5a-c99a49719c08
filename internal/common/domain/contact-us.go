package domain

import "time"

// ContactUs represents public contact and social information.
// Primary key: ID
type ContactUs struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// Email is the contact email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the contact phone
	Phone *string `json:"phone,omitempty" db:"phone"`
	// Facebook profile/page URL
	Facebook *string `json:"facebook,omitempty" db:"facebook"`
	// Instagram profile URL
	Instagram *string `json:"instagram,omitempty" db:"instagram"`
	// Tiktok profile URL
	Tiktok *string `json:"tiktok,omitempty" db:"tiktok"`
	// LineID is the LINE identifier
	LineID *string `json:"line_id,omitempty" db:"line_id"`
	// LineURL is the LINE URL
	LineURL *string `json:"line_url,omitempty" db:"line_url"`
	// Youtube channel URL
	Youtube *string `json:"youtube,omitempty" db:"youtube"`
	// Twitter profile URL
	Twitter *string `json:"twitter,omitempty" db:"twitter"`
	// Linkedin profile URL
	Linkedin *string `json:"linkedin,omitempty" db:"linkedin"`
	// Description is a general description
	Description *string `json:"description,omitempty" db:"description"`
	// FAQ content
	FAQ *string `json:"faq,omitempty" db:"faq"`
	// Policy content
	Policy *string `json:"policy,omitempty" db:"policy"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 