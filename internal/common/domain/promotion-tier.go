package domain

import "time"

// PromotionTier represents the tier rules within a promotion.
// Primary key: ID
// Foreign keys: PromotionID -> promotion.id
type PromotionTier struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// PromotionID references the promotion
	PromotionID *int64 `json:"promotion_id,omitempty" db:"promotion_id"`
	// Description describes this tier
	Description *string `json:"description,omitempty" db:"description"`
	// MinAmount is the minimum amount for the tier
	MinAmount *float64 `json:"min_amount,omitempty" db:"min_amount"`
	// MaxAmount is the maximum amount for the tier
	MaxAmount *float64 `json:"max_amount,omitempty" db:"max_amount"`
	// DiscountType is the type of discount
	DiscountType *string `json:"discount_type,omitempty" db:"discount_type"`
	// DiscountValue is the value of the discount
	DiscountValue *float64 `json:"discount_value,omitempty" db:"discount_value"`
	// Status indicates the current status of the tier
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 