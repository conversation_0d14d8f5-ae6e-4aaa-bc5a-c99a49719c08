package domain

import "time"

// Shop represents a shop location.
// Primary key: ID
type Shop struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// Name is the shop name
	Name *string `json:"name,omitempty" db:"name"`
	// AddressLine1 is the first line of the address
	AddressLine1 *string `json:"address_line1,omitempty" db:"address_line1"`
	// AddressSubdistrict is the subdistrict of the address
	AddressSubdistrict *string `json:"address_subdistrict,omitempty" db:"address_subdistrict"`
	// AddressDistrict is the district of the address
	AddressDistrict *string `json:"address_district,omitempty" db:"address_district"`
	// AddressProvince is the province of the address
	AddressProvince *string `json:"address_province,omitempty" db:"address_province"`
	// AddressZipcode is the postal code of the address
	AddressZipcode *string `json:"address_zipcode,omitempty" db:"address_zipcode"`
	// AreaID is an external area identifier
	AreaID *string `json:"area_id,omitempty" db:"area_id"`
	// Status indicates the current status of the shop
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 