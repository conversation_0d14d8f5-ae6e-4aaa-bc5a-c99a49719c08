package domain

import "time"

// PurchaseLog represents a record of a product purchase with applied promotions.
// Primary key: ID
// Foreign keys: CustomerID -> customer.id, ShopID -> shop.id, StaffID -> staff.id, ProductID -> product.id, PromotionID -> promotion.id, PromotionTierID -> promotion_tier.id
type PurchaseLog struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// ShopID references the shop
	ShopID *int64 `json:"shop_id,omitempty" db:"shop_id"`
	// StaffID references the staff
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// ProductID references the product
	ProductID *int64 `json:"product_id,omitempty" db:"product_id"`
	// PromotionID references the promotion
	PromotionID *int64 `json:"promotion_id,omitempty" db:"promotion_id"`
	// PromotionTierID references the promotion tier
	PromotionTierID *int64 `json:"promotion_tier_id,omitempty" db:"promotion_tier_id"`
	// Price is the purchase price
	Price *float64 `json:"price,omitempty" db:"price"`
	// Status indicates the current status of the purchase
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 