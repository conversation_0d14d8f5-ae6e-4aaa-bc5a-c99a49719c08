package domain

import "time"

// Faq represents a frequently asked question entry.
// Primary key: ID
// Foreign keys: CategoryID -> topic_category.id
type Faq struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// CategoryID references the topic category
	CategoryID *int64 `json:"category_id,omitempty" db:"category_id"`
	// Question is the question text
	Question *string `json:"question,omitempty" db:"question"`
	// Answer is the answer text
	Answer *string `json:"answer,omitempty" db:"answer"`
	// Status indicates the current status of the FAQ entry
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 