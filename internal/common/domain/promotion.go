package domain

import "time"

// Promotion represents a promotion applied to a product.
// Primary key: ID
// Foreign keys: CategoryID -> promotion_category.id, ProductID -> product.id
type Promotion struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// CategoryID references the promotion category
	CategoryID *int64 `json:"category_id,omitempty" db:"category_id"`
	// ProductID references the product
	ProductID *int64 `json:"product_id,omitempty" db:"product_id"`
	// Title is the promotion title
	Title *string `json:"title,omitempty" db:"title"`
	// Description describes the promotion
	Description *string `json:"description,omitempty" db:"description"`
	// Condition is the condition of the promotion
	Condition *string `json:"condition,omitempty" db:"condition"`
	// StartDate is when the promotion starts
	StartDate *time.Time `json:"start_date,omitempty" db:"start_date"`
	// EndDate is when the promotion ends
	EndDate *time.Time `json:"end_date,omitempty" db:"end_date"`
	// BannerURL is the URL of the promotion banner
	BannerURL *string `json:"banner_url,omitempty" db:"banner_url"`
	// BannerURLContentType is the banner content type
	BannerURLContentType *string `json:"banner_url_content_type,omitempty" db:"banner_url_content_type"`
	// Status indicates the current status of the promotion
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 