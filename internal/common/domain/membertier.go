package domain

import "time"

// <PERSON><PERSON> represents the membership tier of a customer.
// Primary key: ID
type <PERSON><PERSON> struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// DisplayName is the human-readable name of the membership tier
	DisplayName *string `json:"display_name,omitempty" db:"display_name"`
	// Description provides details about the membership tier
	Description *string `json:"description,omitempty" db:"description"`
	// Status indicates the current status of the membership tier
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 