package domain

import "time"

// TechLead represents a technology installation lead.
// Primary key: ID
// Foreign keys: GroupID -> customer_group.id, CustomerID -> customer.id, StaffID -> staff.id, ProductIDs -> product.id
type TechLead struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the customer group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// StaffID references the staff owner
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// Firstname is the lead's first name
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the lead's last name
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the lead's email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the lead's phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// LineID is the LINE identifier
	LineID *string `json:"lineId,omitempty" db:"lineId"`
	// InstallationAreaType is the installation area type
	InstallationAreaType *string `json:"installation_area_type,omitempty" db:"installation_area_type"`
	// ResidenceType is the type of residence
	ResidenceType *string `json:"residence_type,omitempty" db:"residence_type"`
	// Floors indicates number of floors
	Floors *int32 `json:"floors,omitempty" db:"floors"`
	// PrimaryUse is the primary usage
	PrimaryUse *string `json:"primary_use,omitempty" db:"primary_use"`
	// UsageType indicates usage type
	UsageType *string `json:"usage_type,omitempty" db:"usage_type"`
	// ConcurrentUsers is the number of concurrent users
	ConcurrentUsers *int32 `json:"concurrent_users,omitempty" db:"concurrent_users"`
	// PrimaryTime indicates primary time of use
	PrimaryTime *string `json:"primary_time,omitempty" db:"primary_time"`
	// Equipment describes equipment
	Equipment *string `json:"equipment,omitempty" db:"equipment"`
	// AccessPointQty is the number of access points
	AccessPointQty *int32 `json:"access_point_qty,omitempty" db:"access_point_qty"`
	// AccessPointRemark is a remark for access points
	AccessPointRemark *string `json:"access_point_remark,omitempty" db:"access_point_remark"`
	// IsHasSmartHome indicates smart home presence
	IsHasSmartHome *bool `json:"is_has_smart_home,omitempty" db:"is_has_smart_home"`
	// MartHomeRemark is a remark (typo in schema preserved)
	MartHomeRemark *string `json:"mart_home_remark,omitempty" db:"mart_home_remark"`
	// IsInterestedSolar indicates interest in solar
	IsInterestedSolar *bool `json:"is_interested_solar,omitempty" db:"is_interested_solar"`
	// MonthlyElectricBill is the monthly electricity bill
	MonthlyElectricBill *float64 `json:"monthly_electric_bill,omitempty" db:"monthly_electric_bill"`
	// ProductIDs references a product identifier
	ProductIDs *int64 `json:"product_ids,omitempty" db:"product_ids"`
	// Notes contains additional information
	Notes *string `json:"notes,omitempty" db:"notes"`
	// PreferredContactTime indicates the preferred time to contact
	PreferredContactTime *string `json:"preferred_contact_time,omitempty" db:"preferred_contact_time"`
	// Status indicates the current status of the lead
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 