package domain

import "time"

// CmsLead represents a lead captured via CMS channels.
// Primary key: ID
// Foreign keys: GroupID -> customer_group.id, CustomerID -> customer.id, StaffID -> staff.id
type CmsLead struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the customer group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// StaffID references the staff owner
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// Firstname is the lead's first name
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the lead's last name
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the lead's email
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the lead's phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// LineID is the LINE identifier
	LineID *string `json:"lineId,omitempty" db:"lineId"`
	// Provider is the current service provider
	Provider *string `json:"provider,omitempty" db:"provider"`
	// Price represents a numeric attribute for the lead (e.g., plan price)
	Price *float64 `json:"price,omitempty" db:"price"`
	// DurationMonths represents the duration in months
	DurationMonths *int32 `json:"duration_months,omitempty" db:"duration_months"`
	// ReasonNotSwitch is the reason for not switching
	ReasonNotSwitch *string `json:"reason_not_switch,omitempty" db:"reason_not_switch"`
	// Notes contains additional information
	Notes *string `json:"notes,omitempty" db:"notes"`
	// PreferredContactTime indicates the preferred time to contact
	PreferredContactTime *string `json:"preferred_contact_time,omitempty" db:"preferred_contact_time"`
	// Status indicates the current status of the lead
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 