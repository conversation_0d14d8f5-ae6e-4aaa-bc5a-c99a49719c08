package domain

import "time"

// PointTransaction represents a transaction affecting a customer's points.
// Primary key: ID
// Foreign keys: CustomerID -> customer.id
type PointTransaction struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// CustomerID references the customer
	CustomerID *int64 `json:"customer_id,omitempty" db:"customer_id"`
	// Point is the amount of points in the transaction
	Point *int32 `json:"point,omitempty" db:"point"`
	// ActionType represents the action that caused the transaction
	ActionType *string `json:"action_type,omitempty" db:"action_type"`
	// Remain is the remaining points after the transaction
	Remain *int32 `json:"remain,omitempty" db:"remain"`
	// ExpiredAt is when the points will expire
	ExpiredAt *time.Time `json:"expired_at,omitempty" db:"expired_at"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 