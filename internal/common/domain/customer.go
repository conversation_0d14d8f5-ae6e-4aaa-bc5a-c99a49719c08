package domain

import "time"

// Customer represents a customer record with profile and grouping information.
// Primary key: ID
// Foreign keys: GroupID -> customer_group.id, MembertierID -> membertier.id, ShopIDs -> shop.id, ProductGroupIDs -> product.id
type Customer struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// GroupID references the customer group
	GroupID *int64 `json:"group_id,omitempty" db:"group_id"`
	// MembertierID references the member tier
	MembertierID *int64 `json:"membertier_id,omitempty" db:"membertier_id"`
	// ShopIDs references a shop identifier
	ShopIDs *int64 `json:"shop_ids,omitempty" db:"shop_ids"`
	// ProductGroupIDs references a product identifier (group)
	ProductGroupIDs *int64 `json:"product_group_ids,omitempty" db:"product_group_ids"`
	// Firstname is the first name of the customer
	Firstname *string `json:"firstname,omitempty" db:"firstname"`
	// Lastname is the last name of the customer
	Lastname *string `json:"lastname,omitempty" db:"lastname"`
	// Email is the customer's email address
	Email *string `json:"email,omitempty" db:"email" validate:"omitempty,email"`
	// Phone is the customer's phone number
	Phone *string `json:"phone,omitempty" db:"phone"`
	// LineID is the LINE identifier of the customer
	LineID *string `json:"line_id,omitempty" db:"line_id"`
	// Gender is the customer's gender
	Gender *string `json:"gender,omitempty" db:"gender"`
	// DateOfBirth is the customer's date of birth
	DateOfBirth *time.Time `json:"date_of_birth,omitempty" db:"date_of_birth"`
	// AddressDistrict is the district of the customer's address
	AddressDistrict *string `json:"address_district,omitempty" db:"address_district"`
	// AddressProvince is the province of the customer's address
	AddressProvince *string `json:"address_province,omitempty" db:"address_province"`
	// Education is the customer's education level
	Education *string `json:"education,omitempty" db:"education"`
	// Job is the customer's job
	Job *string `json:"job,omitempty" db:"job"`
	// Salary is the customer's salary description
	Salary *string `json:"salary,omitempty" db:"salary"`
	// ProfileURL is the URL of the customer's profile image
	ProfileURL *string `json:"profile_url,omitempty" db:"profile_url"`
	// ProfileURLContentType is the content type of the profile URL
	ProfileURLContentType *string `json:"profile_url_content_type,omitempty" db:"profile_url_content_type"`
	// Point is the customer's point balance
	Point *int32 `json:"point,omitempty" db:"point"`
	// CreditScore is the customer's credit score
	CreditScore *float64 `json:"credit_score,omitempty" db:"credit_score"`
	// ReferenceCode is a referral or reference code
	ReferenceCode *string `json:"reference_code,omitempty" db:"reference_code"`
	// Status indicates the current status of the customer
	Status *string `json:"status,omitempty" db:"status"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 