package domain

import "time"

// TncPdpaConsent represents a user's consent action on PDPA terms.
// Primary key: ID
// Foreign keys: TncID -> tnc_pdpa.id, UserID -> customer.id or staff.id
type TncPdpaConsent struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// TncID references the PDPA terms
	TncID *int64 `json:"tnc_id,omitempty" db:"tnc_id"`
	// UserID references the consenting user (customer or staff)
	UserID *int64 `json:"user_id,omitempty" db:"user_id"`
	// UserType indicates the type of the user
	UserType *string `json:"user_type,omitempty" db:"user_type"`
	// ConsentType indicates the type of consent
	ConsentType *string `json:"consent_type,omitempty" db:"consent_type"`
	// TermsVersion is the version at consent time
	TermsVersion *string `json:"terms_version,omitempty" db:"terms_version"`
	// TermsSnapshot is the snapshot of terms at acceptance
	TermsSnapshot *string `json:"terms_snapshot,omitempty" db:"terms_snapshot"`
	// TermsAccept indicates whether terms were accepted
	TermsAccept *bool `json:"terms_accept,omitempty" db:"terms_accept"`
	// AcceptedAt is when the terms were accepted
	AcceptedAt *time.Time `json:"accepted_at,omitempty" db:"accepted_at"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 