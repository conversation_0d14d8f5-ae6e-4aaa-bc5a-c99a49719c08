package domain

import "time"

// Checkin represents a staff location check-in.
// Primary key: ID
// Foreign keys: StaffID -> staff.id
type Checkin struct {
	// ID is the primary key
	ID int64 `json:"id" db:"id" validate:"required"`
	// StaffID references the staff who checked in
	StaffID *int64 `json:"staff_id,omitempty" db:"staff_id"`
	// Lat is the latitude coordinate
	Lat *float64 `json:"lat,omitempty" db:"lat"`
	// Lng is the longitude coordinate
	Lng *float64 `json:"lng,omitempty" db:"lng"`
	// CheckedAt is when the check-in occurred
	CheckedAt *time.Time `json:"checked_at,omitempty" db:"checked_at"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 