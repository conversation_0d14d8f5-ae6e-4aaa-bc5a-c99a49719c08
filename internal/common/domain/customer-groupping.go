package domain

import "time"

// CustomerGroupping represents an assignment of customers to a member tier.
// Primary key: ID (no explicit PK in SQL, but includes id column)
// Foreign keys: CustomerIDs -> customer.id, MembertierID -> membertier.id
type CustomerGroupping struct {
	// ID is the identifier of the grouping
	ID *int64 `json:"id,omitempty" db:"id"`
	// CustomerIDs references a customer identifier
	CustomerIDs *int64 `json:"customer_ids,omitempty" db:"customer_ids"`
	// MembertierID references the member tier
	MembertierID *int64 `json:"membertier_id,omitempty" db:"membertier_id"`
	// CreatedAt is the creation timestamp
	CreatedAt *time.Time `json:"created_at,omitempty" db:"created_at"`
	// CreatedByID is the identifier of the creator user
	CreatedByID *string `json:"created_by_id,omitempty" db:"created_by_id"`
	// CreatedBy is the name of the creator user
	CreatedBy *string `json:"created_by,omitempty" db:"created_by"`
	// UpdatedAt is the last update timestamp
	UpdatedAt *time.Time `json:"updated_at,omitempty" db:"updated_at"`
	// UpdatedByID is the identifier of the updater user
	UpdatedByID *string `json:"updated_by_id,omitempty" db:"updated_by_id"`
	// UpdatedBy is the name of the updater user
	UpdatedBy *string `json:"updated_by,omitempty" db:"updated_by"`
} 