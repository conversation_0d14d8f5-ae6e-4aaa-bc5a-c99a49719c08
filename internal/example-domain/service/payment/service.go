package payment

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/example-domain/port/call-api"
	calldb "digital-transformation-api/internal/example-domain/port/call-db"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	callDb  calldb.Port
	callApi callapi.Port
}

func New(
	callDb calldb.Port,
	callApi callapi.Port,
) Service {
	return &service{
		callDb:  callDb,
		callApi: callApi,
	}
}

func (s *service) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.<PERSON>rf("failed when validate request")
		return nil, errs.NewBadRequestError()
	}

	return nil, nil
}
