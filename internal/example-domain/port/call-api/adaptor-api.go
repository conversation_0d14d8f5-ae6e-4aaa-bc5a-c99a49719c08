package callapi

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"
	"digital-transformation-api/libs/reqs"
)

func NewClient() reqs.Client {
	return reqs.NewClient("integrations.example.post")
}

type adaptorAPI struct {
	client reqs.Client
}

func NewAdaptorAPI(client reqs.Client) Port {
	return &adaptorAPI{
		client: client,
	}
}

func (a *adaptorAPI) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	l, end := logs.NewSpanLogAction(l, "CALL API")
	defer end()

	var (
		responseApi      any
		responseApiError error
	)

	resp := a.client.
		Request().
		AddLogger(l).
		SetResult(&responseApi).
		SetError(&responseApiError).
		Do()
	if resp.IsErrorState() {
		l.<PERSON>("failed to call api: %v", responseApiError)
		return nil, errs.NewExternalError()
	} else if resp.Error() != nil {
		l.<PERSON>("failed to call api unknow error: %v", responseApiError)
		return nil, errs.NewExternalError()
	}

	return &Response{}, nil
}
