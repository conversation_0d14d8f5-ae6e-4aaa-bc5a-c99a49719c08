package handler

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/example-domain/port/call-api"
	calldb "digital-transformation-api/internal/example-domain/port/call-db"
	"digital-transformation-api/internal/example-domain/service/payment"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type paymentHandler struct {
	service payment.Service
}

func NewPaymentHandler(service payment.Service) *paymentHandler {
	return &paymentHandler{
		service: service,
	}
}

func (h *paymentHandler) Handle(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request payment.Request
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	response, err := h.service.Execute(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

func BindPaymentRoute(app gins.GinApps) {
	svc := payment.New(
		calldb.NewAdaptorPG(infrastructure.Db),
		callapi.NewAdaptorAPI(callapi.NewClient()),
	)

	hdl := NewPaymentHandler(svc)
	app.Register(
		http.MethodPost,
		"/v1",
		app.ParseRouteContext(hdl.Handle),
	)
}
