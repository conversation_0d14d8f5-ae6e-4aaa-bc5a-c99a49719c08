package staff

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/responses"
)

type Service interface {
	GetStaffInfo(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
	Register(request *RegisterRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
}

type Request struct{}

type RegisterRequest struct {
	EmployeeID string `json:"employeeId" validate:"required"`
	Phone      string `json:"phone" validate:"required"`
}
