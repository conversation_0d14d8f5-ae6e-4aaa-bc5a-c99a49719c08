package handler

import (
	"digital-transformation-api/infrastructure"
	calldb "digital-transformation-api/internal/line/port/call-db"
	"digital-transformation-api/internal/line/service/staff"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type StaffHandler struct {
	service staff.Service
}

func NewStaffHandler(service staff.Service) *StaffHandler {
	return &StaffHandler{service: service}
}

func (h *StaffHandler) Handle(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {

	if rctx.Header.LineToken == "" {
		_ = ctx.Error(errs.NewCustom(400, errs.Err40001, "Missing line-token in header", "Missing line-token in header"))
		ctx.Abort()
		return
	}

	resp, err := h.service.GetStaffInfo(&staff.Request{}, rctx, l)
	if err != nil {
		_ = ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, resp)
}

func (h *StaffHandler) HandleRegister(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	if rctx.Header.LineToken == "" {
		_ = ctx.Error(errs.NewCustom(400, errs.Err40001, "Missing line-token in header", "Missing line-token in header"))
		ctx.Abort()
		return
	}

	var request staff.RegisterRequest
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind request: %v", err)
		_ = ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	resp, err := h.service.Register(&request, rctx, l)
	if err != nil {
		_ = ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, resp)
}

func BindStaffRoute(app gins.GinApps) {
	svc := staff.New(
		calldb.NewAdaptorPG(infrastructure.Db),
	)
	hdl := NewStaffHandler(svc)
	app.Register(
		http.MethodGet,
		"/line/staff/get-info",
		app.ParseRouteContext(hdl.Handle),
	)
	app.Register(
		http.MethodPost,
		"/line/staff/register",
		app.ParseRouteContext(hdl.HandleRegister),
	)
}
