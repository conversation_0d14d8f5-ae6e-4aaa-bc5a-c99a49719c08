CREATE TABLE "membertier"
(
    "id"            integer PRIMARY KEY,
    "display_name"  varchar,
    "description"   varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "customer_group"
(
    "id"            integer PRIMARY KEY,
    "name"          varchar,
    "description"   varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "customer"
(
    "id"                       integer PRIMARY KEY,
    "group_id"                 integer,
    "membertier_id"            integer,
    "shop_ids"                 integer,
    "product_group_ids"        integer,
    "firstname"                varchar,
    "lastname"                 varchar,
    "email"                    varchar,
    "phone"                    varchar,
    "line_id"                  varchar,
    "gender"                   varchar,
    "date_of_birth"            timestamp,
    "address_district"         varchar,
    "address_province"         varchar,
    "education"                varchar,
    "job"                      varchar,
    "salary"                   varchar,
    "profile_url"              varchar,
    "profile_url_content_type" varchar,
    "point"                    int,
    "credit_score"             float,
    "reference_code"           varchar,
    "status"                   varchar,
    "created_at"               timestamp,
    "created_by_id"            varchar,
    "created_by"               varchar,
    "updated_at"               timestamp,
    "updated_by_id"            varchar,
    "updated_by"               varchar
);

CREATE TABLE "customer_groupping"
(
    "id"            integer,
    "customer_ids"  integer,
    "membertier_id" integer,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "tnc_pdpa"
(
    "id"            integer PRIMARY KEY,
    "user_type"     varchar,
    "consent_type"  varchar,
    "terms_version" varchar,
    "title"         varchar,
    "content"       varchar,
    "status"        varchar,
    "release_date"  timestamp,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "tnc_pdpa_consent"
(
    "id"             integer PRIMARY KEY,
    "tnc_id"         integer,
    "user_id"        integer,
    "user_type"      varchar,
    "consent_type"   varchar,
    "terms_version"  varchar,
    "terms_snapshot" varchar,
    "terms_accept"   bool,
    "accepted_at"    timestamp,
    "created_at"     timestamp,
    "created_by_id"  varchar,
    "created_by"     varchar,
    "updated_at"     timestamp,
    "updated_by_id"  varchar,
    "updated_by"     varchar
);

CREATE TABLE "role"
(
    "id"            integer PRIMARY KEY,
    "name"          varchar,
    "permissions"   varchar[],
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "shop"
(
    "id"                  integer PRIMARY KEY,
    "name"                varchar,
    "address_line1"       varchar,
    "address_subdistrict" varchar,
    "address_district"    varchar,
    "address_province"    varchar,
    "address_zipcode"     varchar,
    "area_id"             varchar,
    "status"              varchar,
    "created_at"          timestamp,
    "created_by_id"       varchar,
    "created_by"          varchar,
    "updated_at"          timestamp,
    "updated_by_id"       varchar,
    "updated_by"          varchar
);

CREATE TABLE "staff"
(
    "id"                       integer PRIMARY KEY,
    "shop_id"                  integer,
    "area_id"                  integer,
    "supervisor_id"            integer,
    "role_id"                  integer,
    "permissions"              varchar[],
    "employee_id"              varchar,
    "firstname"                varchar,
    "lastname"                 varchar,
    "email"                    varchar,
    "line_token"               varchar,
    "phone"                    varchar,
    "gender"                   varchar,
    "education"                varchar,
    "date_of_birth"            timestamp,
    "profile_url"              varchar,
    "profile_url_content_type" varchar,
    "reference_code"           varchar,
    "status"                   varchar,
    "created_at"               timestamp,
    "created_by_id"            varchar,
    "created_by"               varchar,
    "updated_at"               timestamp,
    "updated_by_id"            varchar,
    "updated_by"               varchar
);

CREATE TABLE "checkin"
(
    "id"            integer PRIMARY KEY,
    "staff_id"      integer,
    "lat"           float,
    "lng"           float,
    "checked_at"    timestamp,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "product_group"
(
    "id"            integer PRIMARY KEY,
    "name"          varchar,
    "description"   varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "product"
(
    "id"             integer PRIMARY KEY,
    "group_id"       integer,
    "name"           varchar,
    "description"    varchar,
    "sale_price"     float,
    "regular_price"  float,
    "brand"          varchar,
    "type_of_device" varchar,
    "status"         varchar,
    "created_at"     timestamp,
    "created_by_id"  varchar,
    "created_by"     varchar,
    "updated_at"     timestamp,
    "updated_by_id"  varchar,
    "updated_by"     varchar
);

CREATE TABLE "promotion_category"
(
    "id"            integer PRIMARY KEY,
    "name"          varchar,
    "description"   varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "promotion"
(
    "id"                      integer PRIMARY KEY,
    "category_id"             integer,
    "product_id"              integer,
    "title"                   varchar,
    "description"             varchar,
    "condition"               varchar,
    "start_date"              timestamp,
    "end_date"                timestamp,
    "banner_url"              varchar,
    "banner_url_content_type" varchar,
    "status"                  varchar,
    "created_at"              timestamp,
    "created_by_id"           varchar,
    "created_by"              varchar,
    "updated_at"              timestamp,
    "updated_by_id"           varchar,
    "updated_by"              varchar
);

CREATE TABLE "promotion_tier"
(
    "id"             integer PRIMARY KEY,
    "promotion_id"   integer,
    "description"    varchar,
    "min_amount"     float,
    "max_amount"     float,
    "discount_type"  varchar,
    "discount_value" float,
    "status"         varchar,
    "created_at"     timestamp,
    "created_by_id"  varchar,
    "created_by"     varchar,
    "updated_at"     timestamp,
    "updated_by_id"  varchar,
    "updated_by"     varchar
);

CREATE TABLE "purchase_log"
(
    "id"                integer PRIMARY KEY,
    "customer_id"       integer,
    "shop_id"           integer,
    "staff_id"          integer,
    "product_id"        integer,
    "promotion_id"      integer,
    "promotion_tier_id" integer,
    "price"             float,
    "status"            varchar,
    "created_at"        timestamp,
    "created_by_id"     varchar,
    "created_by"        varchar,
    "updated_at"        timestamp,
    "updated_by_id"     varchar,
    "updated_by"        varchar
);

CREATE TABLE "cms_lead"
(
    "id"                     integer PRIMARY KEY,
    "group_id"               integer,
    "customer_id"            integer,
    "staff_id"               integer,
    "firstname"              varchar,
    "lastname"               varchar,
    "email"                  varchar,
    "phone"                  varchar,
    "lineId"                 varchar,
    "provider"               varchar,
    "price"                  float,
    "duration_months"        int,
    "reason_not_switch"      varchar,
    "notes"                  varchar,
    "preferred_contact_time" varchar,
    "status"                 varchar,
    "created_at"             timestamp,
    "created_by_id"          varchar,
    "created_by"             varchar,
    "updated_at"             timestamp,
    "updated_by_id"          varchar,
    "updated_by"             varchar
);

CREATE TABLE "solar_lead"
(
    "id"                     integer PRIMARY KEY,
    "group_id"               integer,
    "customer_id"            integer,
    "staff_id"               integer,
    "firstname"              varchar,
    "lastname"               varchar,
    "email"                  varchar,
    "phone"                  varchar,
    "lineId"                 varchar,
    "monthly_electric_bill"  float,
    "residence_type"         varchar,
    "meter"                  varchar,
    "roof"                   varchar,
    "roof_type"              varchar,
    "roof_remark"            varchar,
    "concurrent_users"       int,
    "concurrent_air"         varchar,
    "electricity_usage"      varchar,
    "notes"                  varchar,
    "preferred_contact_time" varchar,
    "status"                 varchar,
    "created_at"             timestamp,
    "created_by_id"          varchar,
    "created_by"             varchar,
    "updated_at"             timestamp,
    "updated_by_id"          varchar,
    "updated_by"             varchar
);

CREATE TABLE "tech_lead"
(
    "id"                     integer PRIMARY KEY,
    "group_id"               integer,
    "customer_id"            integer,
    "staff_id"               integer,
    "firstname"              varchar,
    "lastname"               varchar,
    "email"                  varchar,
    "phone"                  varchar,
    "lineId"                 varchar,
    "installation_area_type" varchar,
    "residence_type"         varchar,
    "floors"                 int,
    "primary_use"            varchar,
    "usage_type"             varchar,
    "concurrent_users"       int,
    "primary_time"           varchar,
    "equipment"              varchar,
    "access_point_qty"       int,
    "access_point_remark"    varchar,
    "is_has_smart_home"      bool,
    "mart_home_remark"       varchar,
    "is_interested_solar"    bool,
    "monthly_electric_bill"  float,
    "product_ids"            integer,
    "notes"                  varchar,
    "preferred_contact_time" varchar,
    "status"                 varchar,
    "created_at"             timestamp,
    "created_by_id"          varchar,
    "created_by"             varchar,
    "updated_at"             timestamp,
    "updated_by_id"          varchar,
    "updated_by"             varchar
);

CREATE TABLE "tech_regis"
(
    "id"                     integer PRIMARY KEY,
    "group_id"               integer,
    "customer_id"            integer,
    "staff_id"               integer,
    "firstname"              varchar,
    "lastname"               varchar,
    "email"                  varchar,
    "phone"                  varchar,
    "lineId"                 varchar,
    "product_ids"            integer,
    "promotion_ids"          integer,
    "provider"               varchar,
    "price"                  float,
    "residence_type"         varchar,
    "notes"                  varchar,
    "preferred_contact_time" varchar,
    "status"                 varchar,
    "created_at"             timestamp,
    "created_by_id"          varchar,
    "created_by"             varchar,
    "updated_at"             timestamp,
    "updated_by_id"          varchar,
    "updated_by"             varchar
);

CREATE TABLE "lead_log"
(
    "id"             integer PRIMARY KEY,
    "customer_id"    integer,
    "staff_id"       integer,
    "promotion_id"   integer,
    "customer_phone" varchar,
    "remark"         varchar,
    "reason"         varchar,
    "other_reason"   varchar,
    "is_potential"   bool,
    "status"         varchar,
    "created_at"     timestamp,
    "created_by_id"  varchar,
    "created_by"     varchar,
    "updated_at"     timestamp,
    "updated_by_id"  varchar,
    "updated_by"     varchar
);

CREATE TABLE "contact_us"
(
    "id"            integer PRIMARY KEY,
    "email"         varchar,
    "phone"         varchar,
    "facebook"      varchar,
    "instagram"     varchar,
    "tiktok"        varchar,
    "line_id"       varchar,
    "line_url"      varchar,
    "youtube"       varchar,
    "twitter"       varchar,
    "linkedin"      varchar,
    "description"   varchar,
    "faq"           varchar,
    "policy"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "point_setting"
(
    "id"                    integer PRIMARY KEY,
    "price"                 float,
    "expired_after_receive" int,
    "created_at"            timestamp,
    "created_by_id"         varchar,
    "created_by"            varchar,
    "updated_at"            timestamp,
    "updated_by_id"         varchar,
    "updated_by"            varchar
);

CREATE TABLE "point_transaction"
(
    "id"            integer PRIMARY KEY,
    "customer_id"   integer,
    "point"         int,
    "action_type"   varchar,
    "remain"        int,
    "expired_at"    timestamp,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "topic_category"
(
    "id"            integer PRIMARY KEY,
    "name"          varchar,
    "description"   varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

CREATE TABLE "faq"
(
    "id"            integer PRIMARY KEY,
    "category_id"   integer,
    "question"      varchar,
    "answer"        varchar,
    "status"        varchar,
    "created_at"    timestamp,
    "created_by_id" varchar,
    "created_by"    varchar,
    "updated_at"    timestamp,
    "updated_by_id" varchar,
    "updated_by"    varchar
);

ALTER TABLE "staff"
    ADD FOREIGN KEY ("shop_id") REFERENCES "shop" ("id");

ALTER TABLE "customer"
    ADD FOREIGN KEY ("group_id") REFERENCES "customer_group" ("id");

ALTER TABLE "tnc_pdpa_consent"
    ADD FOREIGN KEY ("tnc_id") REFERENCES "tnc_pdpa" ("id");

ALTER TABLE "tnc_pdpa_consent"
    ADD FOREIGN KEY ("user_id") REFERENCES "customer" ("id");

ALTER TABLE "tnc_pdpa_consent"
    ADD FOREIGN KEY ("user_id") REFERENCES "staff" ("id");

ALTER TABLE "point_transaction"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "checkin"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "staff"
    ADD FOREIGN KEY ("role_id") REFERENCES "role" ("id");

ALTER TABLE "staff"
    ADD FOREIGN KEY ("supervisor_id") REFERENCES "staff" ("id");

ALTER TABLE "product"
    ADD FOREIGN KEY ("group_id") REFERENCES "product_group" ("id");

ALTER TABLE "promotion"
    ADD FOREIGN KEY ("product_id") REFERENCES "product" ("id");

ALTER TABLE "promotion"
    ADD FOREIGN KEY ("category_id") REFERENCES "promotion_category" ("id");

ALTER TABLE "promotion_tier"
    ADD FOREIGN KEY ("promotion_id") REFERENCES "promotion" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("shop_id") REFERENCES "shop" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("product_id") REFERENCES "product" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("promotion_id") REFERENCES "promotion" ("id");

ALTER TABLE "purchase_log"
    ADD FOREIGN KEY ("promotion_tier_id") REFERENCES "promotion_tier" ("id");

ALTER TABLE "cms_lead"
    ADD FOREIGN KEY ("group_id") REFERENCES "customer_group" ("id");

ALTER TABLE "cms_lead"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "cms_lead"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "solar_lead"
    ADD FOREIGN KEY ("group_id") REFERENCES "customer_group" ("id");

ALTER TABLE "solar_lead"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "solar_lead"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "tech_lead"
    ADD FOREIGN KEY ("group_id") REFERENCES "customer_group" ("id");

ALTER TABLE "tech_lead"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "tech_lead"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "tech_lead"
    ADD FOREIGN KEY ("product_ids") REFERENCES "product" ("id");

ALTER TABLE "tech_regis"
    ADD FOREIGN KEY ("group_id") REFERENCES "customer_group" ("id");

ALTER TABLE "tech_regis"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "tech_regis"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "tech_regis"
    ADD FOREIGN KEY ("product_ids") REFERENCES "product" ("id");

ALTER TABLE "tech_regis"
    ADD FOREIGN KEY ("promotion_ids") REFERENCES "promotion" ("id");

ALTER TABLE "lead_log"
    ADD FOREIGN KEY ("customer_id") REFERENCES "customer" ("id");

ALTER TABLE "lead_log"
    ADD FOREIGN KEY ("staff_id") REFERENCES "staff" ("id");

ALTER TABLE "lead_log"
    ADD FOREIGN KEY ("promotion_id") REFERENCES "promotion" ("id");

ALTER TABLE "faq"
    ADD FOREIGN KEY ("category_id") REFERENCES "topic_category" ("id");

ALTER TABLE "customer"
    ADD FOREIGN KEY ("membertier_id") REFERENCES "membertier" ("id");

ALTER TABLE "customer"
    ADD FOREIGN KEY ("shop_ids") REFERENCES "shop" ("id");

ALTER TABLE "customer_groupping"
    ADD FOREIGN KEY ("customer_ids") REFERENCES "customer" ("id");

ALTER TABLE "customer_groupping"
    ADD FOREIGN KEY ("membertier_id") REFERENCES "membertier" ("id");

ALTER TABLE "customer"
    ADD FOREIGN KEY ("product_group_ids") REFERENCES "product" ("id");
