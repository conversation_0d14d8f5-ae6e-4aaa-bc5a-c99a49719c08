package reqs

import "time"

type Config struct {
	BaseUrl string        `mapstructure:"baseUrl"`
	Timeout time.Duration `mapstructure:"timeout"`
	Api     `mapstructure:",squash"`
}

type Api struct {
	EnableInsecureSkipVerify bool   `mapstructure:"enableInsecureSkipVerify"`
	Url                      string `mapstructure:"url"`
	Method                   string `mapstructure:"method"`
	ApiKey                   string `mapstructure:"apiKey"`
	SecretKey                string `mapstructure:"secretKey"`
	LetMeIn                  `mapstructure:",squash"`
	OneTrust                 `mapstructure:",squash"`
	FastInspectAuth          `mapstructure:",squash"`
	Line                     `mapstructure:",squash"`
	Facebook                 `mapstructure:",squash"`
	Msal                     `mapstructure:",squash"`
}

type OneTrust struct {
	Prefix             *string `mapstructure:"prefix"`
	PurposesId         *string `mapstructure:"purposesId"`
	RequestInformation *string `mapstructure:"requestInformation"`
}

type LetMeIn struct {
	UserInfo `mapstructure:",squash"`
}

type FastInspectAuth struct {
	GrantType  string `mapstructure:"grantType"`
	ClientInfo `mapstructure:",squash"`
	UserInfo   `mapstructure:",squash"`
}

type Line struct {
	ClientInfo `mapstructure:",squash"`
}

type ClientInfo struct {
	ClientId     string `mapstructure:"clientId"`
	ClientSecret string `mapstructure:"clientSecret"`
}

type UserInfo struct {
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

type Facebook struct {
	ClientInfo `mapstructure:",squash"`

	RedirectUri string `mapstructure:"redirectUri"`
	Version     string `mapstructure:"version"`
}

type Msal struct {
	TenantId string `mapstructure:"tenantId"`
	ClientId string `mapstructure:"clientId"`
}
