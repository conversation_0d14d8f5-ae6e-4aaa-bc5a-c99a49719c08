package gorms

import (
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

func getTableNameFromQuery(query string) string {
	query = strings.Replace(query, "`", "", -1)
	query = strings.Replace(query, `"`, "", -1)

	match := regex.FindStringSubmatch(query)
	if len(match) > 1 {
		if match[1] == "" {
			return match[2]
		}
		return match[1]
	}
	return ErrCantFindTableName
}

func Equal[T any](tx *gorm.DB, column string, value T) *gorm.DB {
	v := reflect.ValueOf(value)

	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return tx
		}

		v = v.Elem()
	}

	if !v.IsValid() || (v.Kind() != reflect.Bool && v.IsZero()) {
		return tx
	}

	return tx.Where(column+" = ?", v.Interface())
}

func Or[T any](tx *gorm.DB, column string, value T) *gorm.DB {
	v := reflect.ValueOf(value)

	if v.<PERSON>() == reflect.Ptr && v.IsNil() || !v.Is<PERSON>alid() || v.IsZero() {
		return tx
	}

	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Slice && v.Len() > 0 {
		conditions := make([]string, v.Len())
		args := make([]any, v.Len())

		for i := 0; i < v.Len(); i++ {
			conditions[i] = column + " = ?"
			args[i] = v.Index(i).Interface()
		}

		return tx.Or(strings.Join(conditions, " OR "), args...)
	}

	return tx.Or(column+" = ?", v.Interface())
}

func In[S ~[]E, E comparable](tx *gorm.DB, column string, v S) *gorm.DB {
	if len(v) > 0 {
		tx = tx.Where(column+" IN ?", v)
	}

	return tx
}

func NotIn[S ~[]E, E comparable](tx *gorm.DB, column string, v S) *gorm.DB {
	if len(v) > 0 {
		tx = tx.Where(column+" NOT IN ?", v)
	}

	return tx
}

func Any[S ~[]E, E comparable](tx *gorm.DB, column string, v S, t ...string) *gorm.DB {
	var dbType string = "VARCHAR"
	if len(t) > 0 {
		dbType = t[0]
	}

	if len(v) > 0 {
		var items []string
		for _, v := range v {
			items = append(items, fmt.Sprintf("'%v'", v))
		}

		tx = tx.Where(fmt.Sprintf("%s && ARRAY[%s]::%s[]", column, strings.Join(items, ","), dbType))
	}

	return tx
}

func AnyWithEmpty[S ~[]E, E comparable](tx *gorm.DB, column string, v S, t ...string) *gorm.DB {
	var dbType string = "VARCHAR"
	if len(t) > 0 {
		dbType = t[0]
	}

	if len(v) > 0 {
		var items []string
		for _, v := range v {
			items = append(items, fmt.Sprintf("'%v'", v))
		}

		tx = tx.Where(fmt.Sprintf(`(%[1]s && ARRAY[%[2]s]::%[3]s[] OR %[1]s = '{}' OR %[1]s IS NULL)`, column, strings.Join(items, ","), dbType))
	}

	return tx
}

func Limit(tx *gorm.DB, pageSize int64) *gorm.DB {
	if pageSize > 0 {
		tx = tx.Limit(int(pageSize))
	}

	return tx
}

func Offset(tx *gorm.DB, pageNo, pageSize int64) *gorm.DB {
	if pageNo > 0 && pageSize > 0 {
		offset := (pageNo - 1) * pageSize
		tx = tx.Offset(int(offset))
	}

	return tx
}
