package responses

import (
	"digital-transformation-api/libs/apps"
)

// ServiceResponse represents a response from service layer that includes version
type ServiceResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Version string      `json:"version"`
}

// NewServiceResponse creates a new service response with the current API version
func NewServiceResponse(success bool, data interface{}) *ServiceResponse {
	return &ServiceResponse{
		Success: success,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// NewServiceSuccessResponse creates a new successful service response with the current API version
func NewServiceSuccessResponse(data interface{}) *ServiceResponse {
	return &ServiceResponse{
		Success: true,
		Data:    data,
		Version: apps.ApiVersion,
	}
}

// NewServiceSuccessResponseWithMessage creates a new successful service response with a message and the current API version
func NewServiceSuccessResponseWithMessage(data interface{}, message string) *ServiceResponse {
	return &ServiceResponse{
		Success: true,
		Data:    data,
		Message: message,
		Version: apps.ApiVersion,
	}
}
