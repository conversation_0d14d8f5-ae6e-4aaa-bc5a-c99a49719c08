package middleware

import (
	"digital-transformation-api/libs/errs"
	"net/http"

	"github.com/gin-gonic/gin"
)

func Error() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Next()

		for _, err := range ctx.Errors {
			if e, ok := err.Err.(errs.Error); !ok {
				ctx.AbortWithStatusJSON(http.StatusInternalServerError, errs.NewInternalError())
			} else {
				ctx.AbortWithStatusJSON(e.Status(), e)
			}
		}
	}
}
